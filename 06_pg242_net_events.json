{"routers": ["A", "B", "C", "D", "E", "F"], "clients": ["a", "b", "c", "d", "e", "f"], "client_send_rate": 10, "end_time": 400, "links": [["A", "B", 1, 1, 3, 3], ["A", "D", 2, 4, 1, 1], ["A", "F", 3, 1, 6, 6], ["B", "C", 2, 1, 4, 4], ["B", "E", 3, 2, 1, 1], ["C", "D", 2, 1, 1, 1], ["D", "E", 2, 3, 1, 1], ["E", "F", 4, 2, 2, 2], ["a", "A", 1, 4, 1, 1], ["b", "B", 1, 4, 1, 1], ["c", "C", 1, 3, 1, 1], ["d", "D", 1, 3, 1, 1], ["e", "E", 1, 5, 1, 1], ["f", "F", 1, 3, 1, 1]], "changes": [[12, ["C", "D"], "down"], [24, ["C", "D", 2, 1, 9, 9], "up"], [24, ["A", "D"], "down"], [32, ["A", "E", 2, 1, 1, 1], "up"]], "correct_routes": [["a", "A", "a"], ["a", "A", "E", "B", "b"], ["a", "A", "E", "B", "C", "c"], ["a", "A", "E", "D", "d"], ["a", "A", "E", "e"], ["a", "A", "E", "F", "f"], ["b", "B", "b"], ["b", "B", "E", "A", "a"], ["b", "B", "C", "c"], ["b", "B", "E", "D", "d"], ["b", "B", "E", "e"], ["b", "B", "E", "F", "f"], ["c", "C", "c"], ["c", "C", "B", "E", "A", "a"], ["c", "C", "B", "b"], ["c", "C", "B", "E", "D", "d"], ["c", "C", "B", "E", "e"], ["c", "C", "B", "E", "F", "f"], ["d", "D", "d"], ["d", "D", "E", "A", "a"], ["d", "D", "E", "B", "b"], ["d", "D", "E", "B", "C", "c"], ["d", "D", "E", "e"], ["d", "D", "E", "F", "f"], ["e", "E", "e"], ["e", "E", "A", "a"], ["e", "E", "B", "b"], ["e", "E", "B", "C", "c"], ["e", "E", "D", "d"], ["e", "E", "F", "f"], ["f", "F", "f"], ["f", "F", "E", "A", "a"], ["f", "F", "E", "B", "b"], ["f", "F", "E", "B", "C", "c"], ["f", "F", "E", "D", "d"], ["f", "F", "E", "e"]], "visualize": {"grid_size": 4, "locations": {"A": [1, 0], "B": [1, 1], "C": [1, 2], "D": [2, 2], "E": [2, 1], "F": [2, 0], "a": [0, 0], "b": [0, 1], "c": [0, 2], "d": [3, 2], "e": [3, 1], "f": [3, 0]}, "canvas_width": 800, "canvas_height": 800, "time_multiplier": 20, "latency_correction": 1.5, "animate_rate": 40, "router_color": "red", "client_color": "DodgerBlue2", "line_color": "orange", "inactiveColor": "gray", "line_width": 6, "line_font_size": 16}}