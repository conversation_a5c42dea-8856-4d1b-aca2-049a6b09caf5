{"routers": ["A", "B", "C", "D", "E", "F", "G"], "clients": ["a", "b", "c", "d", "e", "f", "g"], "client_send_rate": 10, "end_time": 400, "links": [["A", "B", 1, 1, 1, 1], ["A", "C", 2, 1, 1, 1], ["A", "E", 3, 1, 1, 1], ["A", "F", 4, 1, 1, 1], ["B", "C", 2, 2, 1, 1], ["C", "D", 3, 1, 1, 1], ["a", "A", 1, 5, 1, 1], ["b", "B", 1, 3, 1, 1], ["c", "C", 1, 4, 1, 1], ["d", "D", 1, 3, 1, 1], ["e", "E", 1, 2, 1, 1], ["f", "F", 1, 3, 1, 1], ["g", "G", 1, 3, 1, 1], ["E", "G", 3, 4, 1, 1]], "changes": [[12, ["G", "F", 2, 2, 1, 1], "up"], [24, ["D", "G", 2, 1, 1, 1], "up"], [32, ["E", "G"], "down"]], "correct_routes": [["a", "A", "a"], ["a", "A", "B", "b"], ["a", "A", "C", "c"], ["a", "A", "C", "D", "d"], ["a", "A", "E", "e"], ["a", "A", "F", "f"], ["a", "A", "F", "G", "g"], ["b", "B", "b"], ["b", "B", "A", "a"], ["b", "B", "C", "c"], ["b", "B", "C", "D", "d"], ["b", "B", "A", "E", "e"], ["b", "B", "A", "F", "f"], ["b", "B", "C", "D", "G", "g"], ["b", "B", "A", "F", "G", "g"], ["c", "C", "c"], ["c", "C", "A", "a"], ["c", "C", "B", "b"], ["c", "C", "D", "d"], ["c", "C", "A", "E", "e"], ["c", "C", "A", "F", "f"], ["c", "C", "D", "G", "g"], ["d", "D", "d"], ["d", "D", "C", "A", "a"], ["d", "D", "C", "B", "b"], ["d", "D", "C", "c"], ["d", "D", "C", "A", "E", "e"], ["d", "D", "G", "F", "f"], ["d", "D", "G", "g"], ["e", "E", "e"], ["e", "E", "A", "a"], ["e", "E", "A", "B", "b"], ["e", "E", "A", "C", "c"], ["e", "E", "A", "C", "D", "d"], ["e", "E", "A", "F", "f"], ["e", "E", "A", "F", "G", "g"], ["f", "F", "f"], ["f", "F", "A", "a"], ["f", "F", "A", "B", "b"], ["f", "F", "A", "C", "c"], ["f", "F", "G", "D", "d"], ["f", "F", "A", "E", "e"], ["f", "F", "G", "g"], ["g", "G", "g"], ["g", "G", "F", "A", "a"], ["g", "G", "F", "A", "B", "b"], ["g", "G", "D", "C", "B", "b"], ["g", "G", "D", "C", "c"], ["g", "G", "D", "d"], ["g", "G", "F", "A", "E", "e"], ["g", "G", "F", "f"]], "visualize": {"grid_size": 5, "locations": {"A": [1, 1], "B": [2, 0], "C": [3, 1], "D": [3, 2], "E": [0, 1], "F": [1, 3], "G": [3, 3], "a": [0, 0], "b": [3, 0], "c": [4, 1], "d": [4, 2], "e": [0, 2], "f": [0, 3], "g": [4, 3]}, "canvas_width": 800, "canvas_height": 800, "time_multiplier": 20, "latency_correction": 1.5, "animate_rate": 40, "router_color": "red", "client_color": "DodgerBlue2", "line_color": "orange", "inactiveColor": "gray", "line_width": 6, "line_font_size": 16}}